# UniMERNet LaTeX Processor 函数流程图分析

本文档通过流程图的方式详细描述 `latex_processor.py` 中每个函数的 LaTeX 字符串数据清洗流程。

---

## 1. flatten_multiline 函数流程图

```mermaid
graph TD
    A["输入: 多行LaTeX字符串"] --> B["创建括号映射表"]
    B --> C{"检查是否为array环境"}
    C -->|是| D["分割字符串为token列表"]
    C -->|否| D
    D --> E{"首个token是begin{array}?"}
    E -->|是| F{"末尾是end{array}?"}
    E -->|否| H["开始主循环处理"]
    F -->|是| G["去除首尾环境标记"]
    F -->|否| G1["只去除开头标记"]
    G --> H
    G1 --> H
    H --> I["遍历每个token"]
    I --> J{"token类型判断"}
    J -->|left类括号| K["查找匹配右括号"]
    J -->|换行/空格token| L["删除token并调整索引"]
    J -->|其他| M["继续下一个token"]
    K --> N["跳转到匹配位置"]
    L --> O["idx -= 1"]
    M --> P{"还有token?"}
    N --> P
    O --> P
    P -->|是| I
    P -->|否| Q["重新拼接为字符串"]
    Q --> R["添加数学模式标记"]
    R --> S["输出: 单行数学表达式"]

    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style H fill:#fff3e0
    style S fill:#c8e6c9
```

---

## 2. clean_latex 函数流程图

```mermaid
graph TD
    A["输入: LaTeX字符串"] --> B["正则去除多余空格"]
    B --> C["使用正则模式匹配"]
    C --> D["遍历特殊token列表"]
    D --> E["为特殊token后补回空格"]
    E --> F["处理hline, midrule, times等"]
    F --> G["去除mathcolor前空格"]
    G --> H["输出: 清理后的字符串"]

    style A fill:#e3f2fd
    style B fill:#e1f5fe
    style E fill:#f3e5f5
    style G fill:#fff3e0
    style H fill:#c8e6c9
```

---

## 3. remove_trailing_latex 函数流程图

```mermaid
graph TD
    A["输入: LaTeX公式字符串"] --> B["定义正则模式"]
    B --> C["匹配末尾无用内容"]
    C --> D["包含: hspace, vspace, 标点等"]
    D --> E["执行正则替换"]
    E --> F["count=1, 只替换一次"]
    F --> G["输出: 清理末尾的字符串"]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style E fill:#fff3e0
    style G fill:#c8e6c9
```

---

## 4. find_matching_brace 函数流程图

```mermaid
graph TD
    A[输入: token序列, 起始索引, 括号类型] --> B[解包括号类型]
    B --> C[初始化深度计数器 depth=0]
    C --> D[从起始位置开始遍历]
    D --> E{遇到左括号?}
    E -->|是| F[depth += 1]
    E -->|否| G{遇到右括号?}
    F --> H[继续下一个字符]
    G -->|是| I[depth -= 1]
    G -->|否| H
    I --> J{depth == 0?}
    J -->|是| K[返回当前索引]
    J -->|否| H
    H --> L{还有字符?}
    L -->|是| E
    L -->|否| M{depth > 0?}
    M -->|是| N[抛出ValueError异常]
    M -->|否| O[返回-1]
    
    style F fill:#ffebee
    style I fill:#e3f2fd
    style K fill:#e8f5e8
    style N fill:#ffcdd2
```

---

## 5. normalize_latex 函数流程图（第一部分：初始化和基础处理）

```mermaid
graph TD
    A[输入: LaTeX字符串, rm_trail参数] --> B{包含tabular?}
    B -->|是| C[latex_type = 'tabular']
    B -->|否| D[latex_type = 'formula']
    C --> E{rm_trail为True?}
    D --> E
    E -->|是| F[调用remove_trailing_latex]
    E -->|否| G[跳过末尾清理]
    F --> H[去除首尾空格]
    G --> H
    H --> I[替换\\pmatrix为\\mypmatrix]
    I --> J[替换\\matrix为\\mymatrix]
    J --> K[删除对齐命令]
    K --> L[删除\\raggedright, \\arraybackslash]
    L --> M[删除大小写命令]
    M --> N[删除\\lowercase, \\uppercase]
    N --> O[处理空白命令]
    
    style C fill:#e1f5fe
    style D fill:#f3e5f5
    style F fill:#fff3e0
    style K fill:#ffebee
```
