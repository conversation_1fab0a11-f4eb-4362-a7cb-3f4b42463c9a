| 处理类型 | 处理前 | 处理后 | 备注（涵盖对象/正则/特殊说明） | MathNet |
|----------|--------|--------|-------------------------------|---------|
| 空格去除 | `\frac { a } { b }` | `\frac{a}{b}` | 所有非转义空格，正则：`(?<=[^\\])\s+(?=[^\\])` | - |
| 特殊token补空格 | `a\hlineb` | `a\hline b` | `\hline`、`\midrule`、`\times`、`\bf`、`\footnotesize`、`\cr`、`\log` 后补空格 | - |
| 特定token去空格 | ` \mathcolor{black}{A}` | `\mathcolor{black}{A}` | 仅针对 `\mathcolor{black}` 前空格 | - |
| 末尾无用内容去除 | `a + b;`<br>`a + b \quad`<br>`a + b~`<br>`a + b.` | `a + b` | 末尾 `;`、`,`、`~`、`.`、`\quad`、`\qquad`、`\hspace{}`、`\vspace{}` 等 | - |
| 命令标准化 | `\pmatrix{a}`<br>`\matrix{a}` | `\mypmatrix{a}`<br>`\mymatrix{a}` | 兼容自定义渲染 | - |
| 对齐命令去除 | `a \raggedright b` | `a  b` | `\raggedright`、`\arraybackslash` | - |
| 大小写命令去除 | `\lowercase{A}` | `{A}` | `\lowercase`、`\uppercase` | - |
| 空白命令标准化 | `a \hspace { 1 . 5 cm } b` | `a \hspace{1.5cm} b` | 公式中去空格，表格中去除整个命令 | 不一致：`hskip<数值>(cm\|in\|pt\|mm\|em)` → `hspace{<数值><单位>}` |
| begin/end合并 | `\begin {tabular}` | `\begin{tabular}` | 适用于 `tabular`、`array` 环境 | - |
| 结构token合并 | `\cmidrule ( l { 3 p t } r { 3 p t } ) { 1 - 1 }` | `\cmidrule(l{3pt}r{3pt}){1-1}` | `\cmidrule`、`\cline` 及其参数 | - |
| array列格式标准化 | `\begin{array} { l c r }` | `\begin{array}{lcr}` | 去除多余空格 | √ |
| 特殊符号标准化 | `a -- b`<br>`a --- b`<br>`a … b`<br>`a \dots b` | `a - - b`<br>`a - - - b`<br>`a . . . b`<br>`a . . . b` | 字符映射：`--` → `- -`；`---` → `- - -`；`…` → `. . .`；`\dots` → `. . .`；`\ldots` → `. . .`；`\hdots` → `. . .`；`\cdots` → `. . .`；`\dddot` → `. . .`；`\dotsc` → `. . .`；`\dotsi` → `. . .`；`\dotsm` → `. . .`；`\dotso` → `. . .`；`\dotsb` → `. . .`；`\mathellipsis` → `. . .` | - |
| 数学函数标准化 | `a \sin b`<br>`a \cos b`<br>`a \tanh b` | `a \mathrm { s i n } b`<br>`a \mathrm { c o s } b`<br>`a \mathrm { t a n h } b` | 字符映射：`\sin` → `\mathrm { s i n }`；`\cos` → `\mathrm { c o s }`；`\tan` → `\mathrm { t a n }`；`\tanh` → `\mathrm { t a n h }`；`\cosh` → `\mathrm { c o s h }`；`\sinh` → `\mathrm { s i n h }`；`\coth` → `\mathrm { c o t h }`；`\arcsin` → `\mathrm { a r c s i n }`；`\arccos` → `\mathrm { a r c c o s }`；`\arctan` → `\mathrm { a r c t a n }`；`\exp` → `\mathrm { e x p }`；`\log` → `\mathrm { l o g }`；`\ln` → `\mathrm { l n }`；`\lg` → `\mathrm { l g }`；`\cot` → `\mathrm { c o t }`；`\mod` → `\mathrm { m o d }`；`\bmod` → `\mathrm { m o d }`；`\pmod` → `\mathrm { m o d }`；`\min` → `\mathrm { m i n }`；`\max` → `\mathrm { m a x }`；`\ker` → `\mathrm { k e r }`；`\hom` → `\mathrm { h o m }`；`\sec` → `\mathrm { s e c }`；`\scs` → `\mathrm { s c s }`；`\csc` → `\mathrm { c s c }`；`\deg` → `\mathrm { d e g }`；`\arg` → `\mathrm { a r g }`；`\dim` → `\mathrm { d i m }`；`\ex` → `\mathrm { e x }` | - |
| token合并 | `\string a`<br>`\big (` | `\stringa`<br>`\big(` | `\string`、`\big`、`\Big`、`\bigg`、`\Bigg` 及其后括号/符号 | - |
| operatorname简化 | `\operatorname * {sin}` | `\operatorname {sin}` | 去除 `*` | √ |
| 特定命令去除 | `\lefteqn{A}` | `{A}` | `\lefteqn` | - |
| 特定命令替换 | `\footnote x` | `^ x` | `\footnote` 替换为 `^` | - |
| 特殊token合并 | `\' e` | `\'e` | 仅当后面不是 `{` | - |
| 表格layout合并 | `[ -1.5ex ]` | `[-1.5ex]` | 仅表格，`[\-.0-9 ]+[exptcm ]+` | - |
| parbox合并 | `\parbox { 3cm }` | `\parbox{3cm}` | `\parbox` 后参数合并 | - |
| raisebox合并 | `\raisebox { -1.5ex } [ 0pt ] {` | `\raisebox{-1.5ex}[0pt]{` | `\raisebox` 后参数合并 | - |
| char合并 | `{ \char 39 }` | `{\char39}` | `\char` 后参数合并 | - |
| 规则类命令空格处理 | `\rule {1pt} {2pt}`<br>`\specialrule {1pt} {2pt} {2pt}` | `\rule{1pt}{2pt}`<br>`\specialrule{1pt}{2pt}{2pt}` | `\rule` 和 `\specialrule` 后参数去空格合并 | √ |
| 颜色命令去除 | `\color{red}{A}`<br>`\textcolor[rgb]{0,1,0}{A}` | `{A}` | `\color`、`\colorbox`、`\textcolor`、`\cellcolor` | - |
| 括号/参数补全 | `\hat \lambda`<br>`\sqrt 3 x`<br>`\frac a b` | `\hat {\lambda}`<br>`\sqrt {3} x`<br>`\frac {a} {b}` | 所有一元、二元、可选参数token，见ONE_Tail_Tokens、TWO_Tail_Tokens、AB_Tail_Tokens等 | √ |
| 数学环境标准化 | `\begin{split}`<br>`\begin{align}`<br>`\begin{eqnarray}`<br>`\begin{smallmatrix}` | `\begin{aligned}`<br>`\begin{aligned}`<br>`\begin{aligned}`<br>`\begin{matrix}` | 对齐环境：split、align、alignedat、alignat、eqnarray及其*版本统一为aligned；矩阵环境：小矩阵环境统一为标准矩阵 | - |
| 字体命令标准化 | `{\rm ABC}`<br>`\mbox{text}`<br>`\hbox{text}` | `\mathrm{ABC}`<br>`\mathrm{text}`<br>`\mathrm{text}` | rm格式统一，mbox/hbox转换为mathrm | √ |
| 空白符号处理 | `\~`<br>`\>`<br>`$`<br>`\label{eq1}` | ` ` (空格)<br>` ` (空格)<br>` ` (空格)<br>`` (移除) | 特殊空白符号统一处理 | √ |
| 非矩阵\\处理 | `a \\ b` (非矩阵环境) | `a \, b` | 非矩阵环境中的换行符替换为小空格 | √ |
| 注释移除 | `a + b % 这是注释`<br>`% 整行注释` | `a + b`<br>`` (移除) | % 开头的注释内容完全移除 | √ |
| operatorname反向 | `\operatorname{sin}`<br>`\operatorname{log}` | `\sin`<br>`\log` | 已知函数名从operatorname形式还原 | 不一致：MathNet保持operatorname形式<br>符号算子 → <符号体>    <br>非限制算子 → \\operatorname { <算子名> }  <br>  限制算子 → \\operatorname* { <算子名> } |
| 特殊符号替换 | `SSSSSS`<br>`S S S S S S` | `$`<br>`$` | 连续S或空格分隔S替换为美元符号 | √ |
| 分数根号标准化 | `无横线分数`<br>`有横线分数`<br>`\sqrt[3]{x}` | `\binom{a}{b}`<br>`\frac{a}{b}`<br>`\sqrt[3]{x}` | 基于KaTeX AST的分数根号结构统一 | √ |
| 定界符标准化 | `( a )`<br>`[ b ]` | `\left( a \right)`<br>`\left[ b \right]` | 自动添加left/right前缀 | √ |
| 文本块处理 | `text content` | `\mathrm{text content}` | 所有文本块用mathrm包裹 | √ |
| 空单元格清理 | `\begin{array}{c} {} & a \end{array}` | `\begin{array}{c} & a \end{array}` | 清理数组环境中的空单元格 | √ |
| 列对齐默认值 | `\begin{array}` (无对齐) | `\begin{array}{l}` | 未指定对齐时默认左对齐 | 不一致：MathNet可选择统一为c对齐 |
| 上下标分组 | `a^bc`<br>`x_yz` | `a^{bc}`<br>`x_{yz}` | 非单一字符的上下标自动添加大括号 | √ |
| 大型运算符合并 | `\bigl (`<br>`\Bigr ]`<br>`\big \langle`<br>`\bigg \{` | `\bigl(`<br>`\Bigr]`<br>`\big\langle`<br>`\bigg\{` | 大型运算符与符号合并：扩展运算符（`\bigl`, `\bigr`, `\Bigl`, `\Bigr`, `\biggl`, `\biggr`, `\Biggl`, `\Biggr`, `\bigm`, `\Bigm`, `\biggm`, `\Biggm`）与括号/分隔符合并；LaTeX括号命令（`\langle`, `\rangle`, `\{`, `\|` 等）合并 | √ |
| 字符编码清理 | `\r` | ` ` (空格) | 回车符替换为空格 | + |
| 数学序列处理 | `\mathrm{a b c}` | `\mathrm{a} \; \mathrm{b} \; \mathrm{c}` | mathrm字体中空格转换为`\;`，其他字符后添加空格 | + |
| 分组处理 | `ordgroup内容` | `{内容}` | ordgroup类型自动添加大括号 | + |
| 重音符号处理 | `\hat a`<br>`\hat{abc}` | `\hat{a}`<br>`\hat{abc}` | 非ordgroup的重音符号自动添加大括号 | + |
| 间距处理 | `空格字符` | `~` | 空格字符转换为波浪号 | + |
| 算子处理 | `符号算子`<br>`非限制算子`<br>`限制算子` | `<符号体>`<br>`\operatorname{<算子名>}`<br>`\operatorname*{<算子名>}` | 算子类型标准化 | + |
| 上划线/下划线处理 | `上划线内容`<br>`下划线内容` | `\overline{内容}`<br>`\underline{内容}` | 上下划线标准化 | + |
| 重叠处理 | `左重叠内容`<br>`右重叠内容` | `\llap 内容`<br>`\rlap 内容` | 重叠命令处理 | + |
| 幻影处理 | `幻影内容` | `\phantom{内容}` | 幻影命令标准化 | + |
| Token分割规则 | `\left(`<br>`\right)`<br>`,\;`<br>`\rm\bf` | `[\left, (]`<br>`[\right, )]`<br>`[,, \;]`<br>`[\rm, \bf]` | 复合token按语义分割 | + |
| 空括号移除 | `{ }` | `` (删除) | 移除空的大括号对 | + |
| 非LaTeX命令分割 | `不以\开头且包含\的token` | `按字符分割` | 非标准LaTeX命令处理 | + |
| MathType检测移除 | `包含"M a t h T y p e !"的公式` | `` (完全删除) | 检测并移除MathType公式 | + |
| 首Token检查 | `_开头的公式`<br>`^开头的公式` | `` (删除) | 移除以上下标开头的公式 | + |
| 上下标顺序规范化 | `x^2_1`<br>`x_1^2` | `x_{1}^{2}`<br>`x_{1}^{2}` | 统一为先下标后上标的顺序 | + |
| 样式增强规则 | `字母/希腊字母`<br>`大写字母`<br>`大写字母和数字1` | `\boldsymbol{<字符>}`<br>`\mathcal{<字母>}`<br>`\mathbb{<字符>}` | 可选的样式增强：粗体、花体、黑板粗体 | + |